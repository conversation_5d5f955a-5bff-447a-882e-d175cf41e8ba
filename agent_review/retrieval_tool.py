from smolagents import Tool

from agent_review.retrieval_core import RetrievalCore


class RetrieverTool(Tool):
    name = "retriever"
    description = "Uses semantic search to retrieve relevant parts of user conversation history that could help answer your query."
    inputs = {
        "query": {
            "type": "string",
            "description": "The query to search conversation history. This should be semantically close to the information you're looking for in past conversations. Use the affirmative form rather than a question.",
        }
    }
    output_type = "string"

    def __init__(self, knowledge_base: RetrievalCore):
        super().__init__()
        self.knowledge_base = knowledge_base

    def forward(self, query: str) -> str:
        """Execute the retrieval based on the provided query."""
        assert isinstance(query, str), "Your search query must be a string"

        # Retrieve relevant documents
        docs = self.knowledge_base.search(query)

        # Format the retrieved documents for readability
        return "\nRetrieved documents:\n" + "".join(
            [
                f"\n\n===== Document {str(i)} =====\n" + 
                f"Content: {doc.content}\n" +
                f"Metadata: {doc.meta}\n"
                for i, doc in enumerate(docs)
            ]
        )


# retriever_tool = RetrieverTool()
